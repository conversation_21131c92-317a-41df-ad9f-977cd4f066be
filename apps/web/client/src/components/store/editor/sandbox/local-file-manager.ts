import { api } from '@/trpc/react';
import {
    writeFileAction,
    readFileAction,
    fileExistsAction,
    readDirAction,
} from '@/app/_server/actions/file';
import type { ReaddirEntry } from '@codesandbox/sdk';
import * as fs from 'fs/promises';
import * as path from 'path';

export class LocalFileManager {
    private projectId: string;
    private localPath: string;

    constructor(projectId: string, localPath: string) {
        this.projectId = projectId;
        this.localPath = localPath;

        // Debug logging to verify localPath is correct
        console.debug(
            `LocalFileManager: initialized with projectId: "${projectId}", localPath: "${localPath}"`,
        );

        // Validate that localPath exists and is valid
        if (!localPath) {
            console.error('LocalFileManager: localPath is empty or undefined');
        } else if (!path.isAbsolute(localPath)) {
            console.warn(
                `LocalFileManager: localPath is not absolute: "${localPath}". This may cause issues with file resolution.`,
            );
        } else if (localPath.includes('[username]')) {
            console.error(
                `LocalFileManager: localPath contains placeholder [username]: "${localPath}". Please replace with actual username.`,
            );
        } else {
            console.log(
                `LocalFileManager: Successfully initialized with localPath: "${localPath}"`,
            );
        }
    }

    async writeFile(relativePath: string, content: string | Uint8Array): Promise<void> {
        try {
            const fullPath = this.normalizeFilePath(relativePath);

            if (typeof content === 'string') {
                await writeFileAction(fullPath, content);
            } else {
                // Convert Uint8Array to base64 for binary files
                const base64Content = Buffer.from(content).toString('base64');
                await writeFileAction(fullPath, base64Content, true);
            }
        } catch (error) {
            console.error('Error writing file:', error);
            throw error;
        }
    }

    async readFile(relativePath: string): Promise<string> {
        try {
            const fullPath = this.normalizeFilePath(relativePath);
            return await readFileAction(fullPath);
        } catch (error) {
            console.error('Error reading file:', error);
            throw error;
        }
    }

    async fileExists(relativePath: string): Promise<boolean> {
        try {
            const fullPath = this.normalizeFilePath(relativePath);
            return await fileExistsAction(fullPath);
        } catch (error) {
            return false;
        }
    }

    async copy(
        sourcePath: string,
        targetPath: string,
        recursive?: boolean,
        overwrite?: boolean,
    ): Promise<boolean> {
        // For local projects, we don't implement copy operations since they should be done by the user's local environment
        console.warn('File copy operations are not supported for local projects');
        return false;
    }

    async delete(relativePath: string, recursive?: boolean): Promise<boolean> {
        // For local projects, we don't implement delete operations for safety
        console.warn(
            'File delete operations are not supported for local projects for safety reasons',
        );
        return false;
    }

    async rename(oldPath: string, newPath: string): Promise<boolean> {
        // For local projects, we don't implement rename operations since they should be done by the user's local environment
        console.warn('File rename operations are not supported for local projects');
        return false;
    }

    async readDir(relativePath: string): Promise<ReaddirEntry[]> {
        try {
            const fullPath = this.normalizeFilePath(relativePath);
            const entries = await readDirAction(fullPath);
            return entries.map((entry) => ({
                name: entry.name,
                type: entry.type,
                isSymlink: false, // Default to false since we don't detect symlinks in server action
            }));
        } catch (error) {
            console.error('Error reading directory:', error);
            return [];
        }
    }

    private normalizeFilePath(relativePath: string): string {
        // If localPath is already absolute and we're getting an absolute relativePath,
        // it might be that relativePath is actually the full path already
        if (path.isAbsolute(relativePath)) {
            console.debug(
                `LocalFileManager: relativePath "${relativePath}" is already absolute, using as-is`,
            );
            return relativePath;
        }

        // Remove leading slash if present to make it a true relative path
        const cleanPath = relativePath.startsWith('/') ? relativePath.slice(1) : relativePath;

        // Use path.join for proper path construction
        const fullPath = path.join(this.localPath, cleanPath);

        // Debug logging to help diagnose path issues
        console.debug(
            `LocalFileManager: normalizing path - relative: "${relativePath}" -> clean: "${cleanPath}" -> localPath: "${this.localPath}" -> full: "${fullPath}"`,
        );

        return fullPath;
    }
}
