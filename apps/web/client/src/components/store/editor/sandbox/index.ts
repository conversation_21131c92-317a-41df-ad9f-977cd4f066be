import type { ReaddirEntry, WatchEvent } from '@codesandbox/sdk';
import { EXCLUDED_SYNC_DIRECTORIES, JSX_FILE_EXTENSIONS } from '@onlook/constants';
import { type SandboxFile, type TemplateNode } from '@onlook/models';
import { getContentFromTemplateNode, getTemplateNodeChild } from '@onlook/parser';
import { getBaseName, getDirName, isImageFile, isSubdirectory, LogTimer } from '@onlook/utility';
import { makeAutoObservable, reaction } from 'mobx';
import path from 'path';
import type { EditorEngine } from '../engine';
import { FileEventBus } from './file-event-bus';
import { FileSyncManager } from './file-sync';
import { FileWatcher } from './file-watcher';
import { formatContent, normalizePath } from './helpers';
import { LocalFileManager } from './local-file-manager';
import { TemplateNodeMapper } from './mapping';
import { SessionManager } from './session';

export class SandboxManager {
    readonly session: SessionManager;
    readonly fileEventBus: FileEventBus = new FileEventBus();

    private fileWatcher: FileWatcher | null = null;
    private fileSync: FileSyncManager;
    private templateNodeMap: TemplateNodeMapper;
    private localFileManager: LocalFileManager | null = null;
    private isIndexed = false;
    private isIndexing = false;
    private indexTimeout: NodeJS.Timeout | null = null;

    constructor(private readonly editorEngine: EditorEngine) {
        console.log('🏗️ SandboxManager constructor called');
        this.session = new SessionManager(this.editorEngine);
        this.fileSync = new FileSyncManager();
        this.templateNodeMap = new TemplateNodeMapper();
        makeAutoObservable(this);

        console.log('📋 Setting up session reaction');
        reaction(
            () => this.session.session,
            (session) => {
                this.isIndexed = false;
                if (session) {
                    void this.index();
                }
            },
        );

        console.log('📋 Setting up project data reaction');
        // Initialize local file manager if project is local
        reaction(
            () => this.editorEngine.data.project,
            (project) => {
                void (async () => {
                    console.log('🔄 Project data changed:', {
                        hasProject: !!project,
                        projectName: project?.name,
                        isLocal: project?.isLocal,
                        localPath: project?.localPath,
                    });

                    if (project && project.isLocal === 'true' && project.localPath) {
                        console.log(
                            '🔧 Initializing local file manager for project:',
                            project.name,
                        );
                        this.localFileManager = new LocalFileManager(project.id, project.localPath);

                        // Trigger indexing immediately after local file manager is set up
                        console.log('🚀 Triggering indexing from local file manager reaction...');

                        // Use arrow function to preserve 'this' context and add error handling
                        const timeoutId = setTimeout(() => {
                            void (async () => {
                                try {
                                    console.log('⏰ Timeout fired, calling index(true)');
                                    await this.index(true); // Force re-index
                                    console.log('✅ Index call completed successfully');
                                } catch (error) {
                                    console.error('❌ Error during delayed index call:', error);
                                }
                            })();
                        }, 1000); // Small delay to ensure everything is set up

                        // Store timeout ID for potential cleanup
                        this.indexTimeout = timeoutId;

                        // Also try immediate indexing as a fallback
                        console.log('🔄 Also attempting immediate indexing as fallback...');
                        try {
                            // Use a shorter timeout for immediate attempt
                            setTimeout(() => {
                                void (async () => {
                                    try {
                                        console.log('🚀 Immediate fallback indexing attempt');
                                        await this.index(true);
                                        console.log('✅ Immediate fallback indexing completed');
                                    } catch (error) {
                                        console.error(
                                            '❌ Error during immediate fallback indexing:',
                                            error,
                                        );
                                    }
                                })();
                            }, 100); // Much shorter delay
                        } catch (error) {
                            console.error('❌ Error setting up immediate fallback:', error);
                        }
                    } else {
                        console.log('❌ Not a local project or missing localPath');
                        this.localFileManager = null;
                    }
                })();
            },
        );

        // Also check if project is already loaded (in case reaction doesn't fire)
        const currentProject = this.editorEngine.data.project;
        console.log('🔍 Checking current project state:', {
            hasProject: !!currentProject,
            projectName: currentProject?.name,
            isLocal: currentProject?.isLocal,
            localPath: currentProject?.localPath,
        });

        // Add manual trigger for debugging
        (window as any).debugTriggerLocalFileManager = () => {
            console.log('🐛 Manual trigger for local file manager initialization');
            const project = this.editorEngine.data.project;
            if (project && project.isLocal === 'true' && project.localPath) {
                console.log(
                    '🔧 Manually initializing local file manager for project:',
                    project.name,
                );
                this.localFileManager = new LocalFileManager(project.id, project.localPath);
                console.log('🚀 Manually triggering index');
                this.index(true);
            } else {
                console.log('❌ Cannot manually initialize - invalid project data');
            }
        };

        // Removed delayed check to prevent multiple initialization paths

        console.log('✅ SandboxManager constructor completed');
    }

    async index(force = false) {
        console.log(
            `📋 Index called - force: ${force}, isIndexing: ${this.isIndexing}, isIndexed: ${this.isIndexed}`,
        );

        if (this.isIndexing || (this.isIndexed && !force)) {
            console.log('⏭️  Skipping index - already indexing or indexed');
            return;
        }

        // Handle indexing for local projects
        const project = this.editorEngine.data.project;
        if (project && project.isLocal === 'true') {
            console.log('🚀 Starting local project indexing for project:', project.name);

            // Ensure local file manager is initialized
            if (!this.localFileManager && project.localPath) {
                console.log(
                    '🔧 Initializing local file manager during indexing for project:',
                    project.name,
                );
                this.localFileManager = new LocalFileManager(project.id, project.localPath);
            }

            await this.indexLocalProject();
            this.isIndexed = true;
            console.log('✅ Local project indexing completed');
            return;
        }

        if (!this.session.session) {
            console.error('No session found');
            return;
        }

        this.isIndexing = true;
        const timer = new LogTimer('Sandbox Indexing');

        try {
            // Get all file paths
            const allFilePaths = await this.getAllFilePathsFlat('./', EXCLUDED_SYNC_DIRECTORIES);
            timer.log(`File discovery completed - ${allFilePaths.length} files found`);

            // Categorize files for optimized processing
            const { imageFiles, jsxFiles, otherFiles } =
                this.categorizeFilesForIndexing(allFilePaths);

            const BATCH_SIZE = 50;

            // Track image files first
            if (imageFiles.length > 0) {
                timer.log(`Tracking ${imageFiles.length} image files`);
                for (let i = 0; i < imageFiles.length; i += BATCH_SIZE) {
                    const batch = imageFiles.slice(i, i + BATCH_SIZE);
                    for (const filePath of batch) {
                        await this.fileSync.writeEmptyFile(filePath, 'binary');
                    }
                }
            }

            // Process JSX files
            if (jsxFiles.length > 0) {
                timer.log(`Processing ${jsxFiles.length} JSX files in batches of ${BATCH_SIZE}`);
                for (let i = 0; i < jsxFiles.length; i += BATCH_SIZE) {
                    const batch = jsxFiles.slice(i, i + BATCH_SIZE);
                    await this.processJsxFilesBatch(batch);
                }
            }

            // Process other files
            if (otherFiles.length > 0) {
                timer.log(
                    `Processing ${otherFiles.length} other files in batches of ${BATCH_SIZE}`,
                );
                for (let i = 0; i < otherFiles.length; i += BATCH_SIZE) {
                    const batch = otherFiles.slice(i, i + BATCH_SIZE);
                    await this.processTextFilesBatch(batch);
                }
            }

            await this.watchFiles();
            this.isIndexed = true;
            timer.log('Indexing completed successfully');
        } catch (error) {
            console.error('Error during indexing:', error);
            throw error;
        } finally {
            this.isIndexing = false;
        }
    }

    /**
     * Optimized flat file discovery - similar to hosting manager approach
     */
    private async getAllFilePathsFlat(rootDir: string, excludeDirs: string[]): Promise<string[]> {
        if (!this.session.session) {
            throw new Error('No session available for file discovery');
        }

        const allPaths: string[] = [];
        const dirsToProcess = [rootDir];

        while (dirsToProcess.length > 0) {
            const currentDir = dirsToProcess.shift()!;
            try {
                const entries = await this.session.session.fs.readdir(currentDir);

                for (const entry of entries) {
                    const fullPath = `${currentDir}/${entry.name}`;
                    const normalizedPath = normalizePath(fullPath);

                    if (entry.type === 'directory') {
                        // Skip excluded directories
                        if (!excludeDirs.includes(entry.name)) {
                            dirsToProcess.push(normalizedPath);
                        }
                    } else if (entry.type === 'file') {
                        allPaths.push(normalizedPath);
                    }
                }
            } catch (error) {
                console.warn(`Error reading directory ${currentDir}:`, error);
            }
        }

        return allPaths;
    }

    /**
     * Categorize files for optimized processing
     */
    private categorizeFilesForIndexing(filePaths: string[]): {
        imageFiles: string[];
        jsxFiles: string[];
        otherFiles: string[];
    } {
        const imageFiles: string[] = [];
        const jsxFiles: string[] = [];
        const otherFiles: string[] = [];

        for (const filePath of filePaths) {
            const normalizedPath = normalizePath(filePath);

            if (isImageFile(normalizedPath)) {
                imageFiles.push(normalizedPath);
            } else {
                const extension = path.extname(filePath);
                if (JSX_FILE_EXTENSIONS.includes(extension)) {
                    jsxFiles.push(normalizedPath);
                } else {
                    otherFiles.push(normalizedPath);
                }
            }
        }

        return { imageFiles, jsxFiles, otherFiles };
    }

    private async processJsxFilesBatch(filePaths: string[]): Promise<void> {
        const fileContents = await this.fileSync.readOrFetchBatch(
            filePaths,
            this.readRemoteFile.bind(this),
        );

        const mappingPromises = Object.keys(fileContents).map(async (filePath) => {
            try {
                await this.processFileForMapping(filePath);
            } catch (error) {
                console.warn(`Error processing mapping for JSX file ${filePath}:`, error);
            }
        });

        await Promise.all(mappingPromises);
    }

    /**
     * Process text files in parallel batches
     */
    private async processTextFilesBatch(filePaths: string[]): Promise<void> {
        await this.fileSync.readOrFetchBatch(filePaths, this.readRemoteFile.bind(this));
    }

    private async readRemoteFile(filePath: string): Promise<SandboxFile | null> {
        if (!this.session.session) {
            console.error('No session found for remote read');
            throw new Error('No session found for remote read');
        }

        try {
            if (isImageFile(filePath)) {
                const content = await this.session.session.fs.readFile(filePath);
                return this.fileSync.getFileFromContent(filePath, content);
            } else {
                const content = await this.session.session.fs.readTextFile(filePath);
                return this.fileSync.getFileFromContent(filePath, content);
            }
        } catch (error) {
            console.error(`Error reading remote file ${filePath}:`, error);
            return null;
        }
    }

    private async writeRemoteFile(
        filePath: string,
        content: string | Uint8Array,
    ): Promise<boolean> {
        if (!this.session.session) {
            console.error('No session found for remote write');
            return false;
        }

        try {
            await this.processFileForMapping(filePath);
            if (content instanceof Uint8Array) {
                await this.session.session.fs.writeFile(filePath, content);
            } else {
                await this.session.session.fs.writeTextFile(filePath, content);
            }
            return true;
        } catch (error) {
            console.error(`Error writing remote file ${filePath}:`, error);
            return false;
        }
    }

    async readFile(path: string): Promise<SandboxFile | null> {
        const normalizedPath = normalizePath(path);

        // For local projects, use local file manager
        const project = this.editorEngine.data.project;
        if (project && project.isLocal === 'true') {
            // Initialize local file manager if not already done
            if (!this.localFileManager && project.localPath) {
                this.localFileManager = new LocalFileManager(project.id, project.localPath);
            }

            if (this.localFileManager) {
                try {
                    const content = await this.localFileManager.readFile(normalizedPath);
                    return {
                        type: 'text',
                        path: normalizedPath,
                        content: content,
                    };
                } catch (error) {
                    console.error('Error reading local file:', error);
                    return null;
                }
            }
        }

        return this.fileSync.readOrFetch(normalizedPath, this.readRemoteFile.bind(this));
    }

    async readFiles(paths: string[]): Promise<Record<string, SandboxFile>> {
        const results: Map<string, SandboxFile> = new Map();
        for (const path of paths) {
            const file = await this.readFile(path);
            if (!file) {
                console.error(`Failed to read file ${path}`);
                continue;
            }
            results.set(path, file);
        }
        return Object.fromEntries(results);
    }

    async writeFile(path: string, content: string): Promise<boolean> {
        const normalizedPath = normalizePath(path);
        const formattedContent = await formatContent(normalizedPath, content);

        // Check if this is a local project
        const project = this.editorEngine.data.project;
        if (project && project.isLocal === 'true') {
            // Initialize local file manager if not already done
            if (!this.localFileManager && project.localPath) {
                this.localFileManager = new LocalFileManager(project.id, project.localPath);
            }

            if (this.localFileManager) {
                try {
                    // Process the file through template node mapper to update OID mapping
                    await this.templateNodeMap.processFileForMapping(
                        normalizedPath,
                        async (path) => {
                            // Return the new content that we're about to write
                            return { content: formattedContent, type: 'text' as const, path };
                        },
                        async (path, processedContent) => {
                            // Write the processed content (which may include OIDs)
                            await this.localFileManager!.writeFile(path, processedContent);
                            // Update the cache with processed content
                            this.fileSync.updateCache({
                                type: 'text',
                                path: normalizedPath,
                                content: processedContent,
                            });
                            return true;
                        },
                    );
                    return true;
                } catch (error) {
                    console.error('Error writing local file:', error);
                    return false;
                }
            }
        }

        return this.fileSync.write(
            normalizedPath,
            formattedContent,
            this.writeRemoteFile.bind(this),
        );
    }

    async writeBinaryFile(path: string, content: Buffer | Uint8Array): Promise<boolean> {
        const normalizedPath = normalizePath(path);
        try {
            return this.fileSync.write(normalizedPath, content, this.writeRemoteFile.bind(this));
        } catch (error) {
            console.error(`Error writing binary file ${normalizedPath}:`, error);
            return false;
        }
    }

    get files() {
        return this.fileSync.listAllFiles();
    }

    listAllFiles() {
        return this.fileSync.listAllFiles();
    }

    async readDir(dir: string): Promise<ReaddirEntry[]> {
        // For local projects, use local file manager
        const project = this.editorEngine.data.project;
        if (project && project.isLocal === 'true') {
            // Initialize local file manager if not already done
            if (!this.localFileManager && project.localPath) {
                this.localFileManager = new LocalFileManager(project.id, project.localPath);
            }

            if (this.localFileManager) {
                try {
                    return await this.localFileManager.readDir(dir);
                } catch (error) {
                    console.error('Error reading local directory:', error);
                    return [];
                }
            }
        }

        return this.session.session?.fs.readdir(dir) ?? Promise.resolve([]);
    }

    async listFilesRecursively(
        dir: string,
        ignoreDirs: string[] = [],
        ignoreExtensions: string[] = [],
    ): Promise<string[]> {
        if (!this.session.session) {
            console.error('No session found');
            return [];
        }

        const results: string[] = [];
        const entries = await this.session.session.fs.readdir(dir);

        for (const entry of entries) {
            const fullPath = `${dir}/${entry.name}`;
            const normalizedPath = normalizePath(fullPath);
            if (entry.type === 'directory') {
                if (ignoreDirs.includes(entry.name)) {
                    continue;
                }
                const subFiles = await this.listFilesRecursively(
                    normalizedPath,
                    ignoreDirs,
                    ignoreExtensions,
                );
                results.push(...subFiles);
            } else {
                const extension = path.extname(entry.name);
                if (ignoreExtensions.length > 0 && !ignoreExtensions.includes(extension)) {
                    continue;
                }
                results.push(normalizedPath);
            }
        }
        return results;
    }

    // Download the code as a zip
    async downloadFiles(
        projectName?: string,
    ): Promise<{ downloadUrl: string; fileName: string } | null> {
        // For local projects, download is not supported since files are already local
        const project = this.editorEngine.data.project;
        if (project && project.isLocal === 'true') {
            console.log(
                'Download not available for local projects - files are already on your local filesystem',
            );
            return null;
        }

        if (!this.session.session) {
            console.error('No sandbox session found');
            return null;
        }
        try {
            const { downloadUrl } = await this.session.session.fs.download('./');
            return {
                downloadUrl,
                fileName: `${projectName || 'onlook-project'}-${Date.now()}.zip`,
            };
        } catch (error) {
            console.error('Error generating download URL:', error);
            return null;
        }
    }

    async watchFiles() {
        // Skip file watching for local projects
        const project = this.editorEngine.data.project;
        if (project && project.isLocal === 'true') {
            return;
        }

        if (!this.session.session) {
            console.error('No session found');
            return;
        }

        // Dispose of existing watcher if it exists
        if (this.fileWatcher) {
            this.fileWatcher.dispose();
            this.fileWatcher = null;
        }

        // Convert ignored directories to glob patterns with ** wildcard
        const excludePatterns = EXCLUDED_SYNC_DIRECTORIES.map((dir) => `${dir}/**`);

        this.fileWatcher = new FileWatcher({
            session: this.session.session,
            onFileChange: async (event) => {
                await this.handleFileChange(event);
            },
            excludePatterns,
            fileEventBus: this.fileEventBus,
        });

        await this.fileWatcher.start();
    }

    async handleFileChange(event: WatchEvent) {
        const eventType = event.type;

        if (eventType === 'remove') {
            for (const path of event.paths) {
                if (isSubdirectory(path, EXCLUDED_SYNC_DIRECTORIES)) {
                    continue;
                }
                const normalizedPath = normalizePath(path);
                await this.fileSync.delete(normalizedPath);

                this.fileEventBus.publish({
                    type: eventType,
                    paths: [normalizedPath],
                    timestamp: Date.now(),
                });
            }
        } else if (eventType === 'change' || eventType === 'add') {
            if (event.paths.length === 2) {
                // This mean rename a file or a folder, move a file or a folder
                const [oldPath, newPath] = event.paths;

                if (!oldPath || !newPath) {
                    console.error('Invalid rename event', event);
                    return;
                }
                const oldNormalizedPath = normalizePath(oldPath);
                const newNormalizedPath = normalizePath(newPath);
                await this.fileSync.rename(oldNormalizedPath, newNormalizedPath);

                this.fileEventBus.publish({
                    type: 'rename',
                    paths: [oldPath, newPath],
                    timestamp: Date.now(),
                });
                return;
            }
            for (const path of event.paths) {
                if (isSubdirectory(path, EXCLUDED_SYNC_DIRECTORIES)) {
                    continue;
                }
                const normalizedPath = normalizePath(path);

                if (isImageFile(normalizedPath)) {
                    await this.fileSync.writeEmptyFile(normalizedPath, 'binary');
                } else {
                    const content = await this.readRemoteFile(normalizedPath);
                    if (content === null) {
                        console.error(`File content for ${normalizedPath} not found`);
                        continue;
                    }
                    const cachedFile = this.fileSync.readCache(normalizedPath);
                    if (
                        !cachedFile ||
                        cachedFile.content === null ||
                        cachedFile.content !== content.content
                    ) {
                        await this.processFileForMapping(normalizedPath);
                        this.fileSync.updateCache({
                            type: 'text',
                            path: normalizedPath,
                            content: content.content as string,
                        });
                    }
                }

                this.fileEventBus.publish({
                    type: eventType,
                    paths: [normalizedPath],
                    timestamp: Date.now(),
                });
            }
        }
    }

    async processFileForMapping(file: string) {
        const extension = path.extname(file);
        if (!extension || !JSX_FILE_EXTENSIONS.includes(extension)) {
            return;
        }

        const normalizedPath = normalizePath(file);
        await this.templateNodeMap.processFileForMapping(
            normalizedPath,
            this.readFile.bind(this),
            this.writeFile.bind(this),
        );
    }

    async getTemplateNode(oid: string): Promise<TemplateNode | null> {
        const templateNode = this.templateNodeMap.getTemplateNode(oid);

        if (!templateNode) {
            // Debug: Log what's actually in the template node map
            const allOids = Array.from(this.templateNodeMap.getTemplateNodeMap().keys());

            // Check if this might be a similar OID
            const similarOids = allOids.filter(
                (availableOid) =>
                    availableOid.includes(oid.slice(-4)) || oid.includes(availableOid.slice(-4)),
            );
            if (similarOids.length > 0) {
                console.log(`🔍 Similar OIDs found:`, similarOids);
            }
        } else {
            console.log(`✅ Found template node for oid: ${oid}`);
        }

        return templateNode;
    }

    async getTemplateNodeChild(
        parentOid: string,
        child: TemplateNode,
        index: number,
    ): Promise<{ instanceId: string; component: string } | null> {
        const codeBlock = await this.getCodeBlock(parentOid);

        if (codeBlock == null) {
            console.error(`Failed to read code block: ${parentOid}`);
            return null;
        }

        return await getTemplateNodeChild(codeBlock, child, index);
    }

    async getCodeBlock(oid: string): Promise<string | null> {
        const templateNode = this.templateNodeMap.getTemplateNode(oid);
        if (!templateNode) {
            console.error(`No template node found for oid ${oid}`);
            return null;
        }

        const file = await this.readFile(templateNode.path);
        if (!file) {
            console.error(`No file found for template node ${oid}`);
            return null;
        }

        if (file.type === 'binary') {
            console.error(`File ${templateNode.path} is a binary file`);
            return null;
        }

        const codeBlock = await getContentFromTemplateNode(templateNode, file.content);
        return codeBlock;
    }

    async fileExists(path: string): Promise<boolean> {
        let normalizedPath = normalizePath(path);

        // For local projects, use local file manager
        const project = this.editorEngine.data.project;
        if (project && project.isLocal === 'true') {
            // Initialize local file manager if not already done
            if (!this.localFileManager && project.localPath) {
                this.localFileManager = new LocalFileManager(project.id, project.localPath);
            }

            if (this.localFileManager) {
                try {
                    normalizedPath = `${project.localPath}/${normalizedPath}`;
                    return await this.localFileManager.fileExists(normalizedPath);
                } catch (error) {
                    console.error('Error checking local file existence:', error);
                    return false;
                }
            } else {
                console.error('Local file manager not available for local project');
                return false;
            }
        }

        if (!this.session.session) {
            console.error('No session found for file existence check');
            return false;
        }

        try {
            const dirPath = getDirName(normalizedPath);
            const fileName = getBaseName(normalizedPath);
            const dirEntries = await this.session.session.fs.readdir(dirPath);
            return dirEntries.some((entry: any) => entry.name === fileName);
        } catch (error) {
            console.error(`Error checking file existence ${normalizedPath}:`, error);
            return false;
        }
    }

    async copy(
        path: string,
        targetPath: string,
        recursive?: boolean,
        overwrite?: boolean,
    ): Promise<boolean> {
        const normalizedSourcePath = normalizePath(path);
        const normalizedTargetPath = normalizePath(targetPath);

        // For local projects, use local file manager
        const project = this.editorEngine.data.project;
        if (project && project.isLocal === 'true') {
            // Initialize local file manager if not already done
            if (!this.localFileManager && project.localPath) {
                this.localFileManager = new LocalFileManager(project.id, project.localPath);
            }

            if (this.localFileManager) {
                try {
                    return await this.localFileManager.copy(
                        normalizedSourcePath,
                        normalizedTargetPath,
                        recursive,
                        overwrite,
                    );
                } catch (error) {
                    console.error('Error copying local file:', error);
                    return false;
                }
            }
        }

        if (!this.session.session) {
            console.error('No session found for copy');
            return false;
        }

        try {
            // Check if source exists
            const sourceExists = await this.fileExists(normalizedSourcePath);
            if (!sourceExists) {
                console.error(`Source ${normalizedSourcePath} does not exist`);
                return false;
            }

            await this.session.session.fs.copy(
                normalizedSourcePath,
                normalizedTargetPath,
                recursive,
                overwrite,
            );

            return true;
        } catch (error) {
            console.error(`Error copying ${path} to ${targetPath}:`, error);
            return false;
        }
    }

    async delete(path: string, recursive?: boolean): Promise<boolean> {
        const normalizedPath = normalizePath(path);

        // For local projects, use local file manager
        const project = this.editorEngine.data.project;
        if (project && project.isLocal === 'true') {
            // Initialize local file manager if not already done
            if (!this.localFileManager && project.localPath) {
                this.localFileManager = new LocalFileManager(project.id, project.localPath);
            }

            if (this.localFileManager) {
                try {
                    const result = await this.localFileManager.delete(normalizedPath, recursive);

                    // Clean up the file sync cache
                    await this.fileSync.delete(normalizedPath);

                    // Publish file deletion event
                    this.fileEventBus.publish({
                        type: 'remove',
                        paths: [normalizedPath],
                        timestamp: Date.now(),
                    });

                    console.log(`Successfully deleted local file: ${normalizedPath}`);
                    return result;
                } catch (error) {
                    console.error('Error deleting local file:', error);
                    return false;
                }
            }
        }

        if (!this.session.session) {
            console.error('No session found for delete file');
            return false;
        }

        try {
            // Check if file exists before attempting to delete
            const exists = await this.fileExists(normalizedPath);
            if (!exists) {
                console.error(`File ${normalizedPath} does not exist`);
                return false;
            }

            // Delete the file using the filesystem API
            await this.session.session.fs.remove(normalizedPath, recursive);

            // Clean up the file sync cache
            await this.fileSync.delete(normalizedPath);

            // Publish file deletion event
            this.fileEventBus.publish({
                type: 'remove',
                paths: [normalizedPath],
                timestamp: Date.now(),
            });

            console.log(`Successfully deleted file: ${normalizedPath}`);
            return true;
        } catch (error) {
            console.error(`Error deleting file ${path}:`, error);
            return false;
        }
    }

    async rename(oldPath: string, newPath: string): Promise<boolean> {
        const normalizedOldPath = normalizePath(oldPath);
        const normalizedNewPath = normalizePath(newPath);

        // For local projects, use local file manager
        const project = this.editorEngine.data.project;
        if (project && project.isLocal === 'true') {
            // Initialize local file manager if not already done
            if (!this.localFileManager && project.localPath) {
                this.localFileManager = new LocalFileManager(project.id, project.localPath);
            }

            if (this.localFileManager) {
                try {
                    return await this.localFileManager.rename(normalizedOldPath, normalizedNewPath);
                } catch (error) {
                    console.error('Error renaming local file:', error);
                    return false;
                }
            }
        }

        if (!this.session.session) {
            console.error('No session found for rename');
            return false;
        }

        try {
            await this.session.session.fs.rename(normalizedOldPath, normalizedNewPath);
            return true;
        } catch (error) {
            console.error(`Error renaming file ${oldPath} to ${newPath}:`, error);
            return false;
        }
    }

    get isIndexingFiles() {
        return this.isIndexing;
    }

    get localFileManagerInstance() {
        // Lazy initialization if not already set
        if (!this.localFileManager) {
            const project = this.editorEngine.data.project;
            console.log('🔍 Lazy initialization check:', {
                hasProject: !!project,
                projectName: project?.name,
                isLocal: project?.isLocal,
                localPath: project?.localPath,
                currentLocalFileManager: !!this.localFileManager,
            });
            if (project && project.isLocal === 'true' && project.localPath) {
                console.log('🔧 Lazy initializing local file manager for project:', project.name);
                this.localFileManager = new LocalFileManager(project.id, project.localPath);
                // DO NOT trigger indexing from the getter to avoid loops
                console.log(
                    '⚠️ Note: Lazy initialization complete, but NOT triggering indexing from getter',
                );
            } else {
                console.log('❌ Cannot lazy initialize - invalid project data');
            }
        }
        return this.localFileManager;
    }

    /**
     * Index local project files to build template node map
     */
    async indexLocalProject() {
        if (!this.localFileManager) {
            console.error('No local file manager available for indexing');
            return;
        }

        console.log('🏗️  Starting local project indexing...');
        this.isIndexing = true;

        try {
            // Scan for all files in the project (not just JSX)
            const allFiles = await this.scanDirectoryRecursive('');
            console.log(`📁 Found ${allFiles.length} total files in project`);

            // Cache all files in fileSync for listAllFiles() to work
            for (const filePath of allFiles) {
                this.fileSync.writeEmptyFile(filePath, 'text');
            }

            // Scan for JSX/TSX files in the project
            const jsxFiles = await this.scanLocalProjectFiles();
            console.log(`📄 Found ${jsxFiles.length} JSX files to index:`, jsxFiles);

            let totalTemplateNodes = 0;

            // Process JSX files to build template node map
            for (const filePath of jsxFiles) {
                try {
                    console.log(`🔄 Indexing file: ${filePath}`);

                    // For local projects, we need to avoid the circular writeFile issue
                    // So we call templateNodeMap.processFileForMapping directly with simple functions
                    await this.templateNodeMap.processFileForMapping(
                        filePath,
                        this.readFile.bind(this), // This handles local projects correctly
                        async (path, content) => {
                            // During indexing, we don't write back to files
                            // The files already have OIDs from project creation
                            console.log(`⏭️  Skipping file write during indexing: ${path}`);
                            return true;
                        },
                    );

                    // Count template nodes added for this file
                    const currentNodes = this.templateNodeMap.getTemplateNodeMap().size;
                    console.log(
                        `📊 Template nodes after processing ${filePath}: ${currentNodes} (added: ${currentNodes - totalTemplateNodes})`,
                    );
                    totalTemplateNodes = currentNodes;
                } catch (error) {
                    console.error(`Error processing file ${filePath}:`, error);
                }
            }

            console.log(
                `🎉 Local project indexing completed. Processed ${jsxFiles.length} files, total template nodes: ${totalTemplateNodes}`,
            );

            // Log some template nodes for debugging
            const templateMap = this.templateNodeMap.getTemplateNodeMap();
            const firstFewOids = Array.from(templateMap.keys()).slice(0, 10);
            console.log('🔍 First 10 OIDs in template map:', firstFewOids);
            console.log('📊 Total template nodes in map:', templateMap.size);

            // Make indexing result available for manual debugging
            (window as any).debugOnlookTemplateMap = templateMap;
            (window as any).debugReindexLocalProject = () => {
                console.log('🔄 Manual re-indexing triggered...');
                return this.indexLocalProject();
            };
            (window as any).debugForceIndex = () => {
                console.log('🔄 Force index triggered...');
                return this.index(true);
            };
        } catch (error) {
            console.error('Error during local project indexing:', error);
        } finally {
            this.isIndexing = false;
        }
    }

    /**
     * Scan local project for JSX/TSX files
     */
    async scanLocalProjectFiles(): Promise<string[]> {
        if (!this.localFileManager) {
            return [];
        }

        const jsxFiles: string[] = [];
        const dirsToScan = ['', 'src', 'app', 'src/app', 'pages', 'src/pages'];

        for (const dir of dirsToScan) {
            try {
                const files = await this.scanDirectoryRecursive(dir);
                jsxFiles.push(...files);
            } catch (_error) {
                // Directory might not exist, which is fine
                console.debug(`Directory ${dir} not found or inaccessible`);
            }
        }

        // Remove duplicates and filter for JSX files
        const uniqueFiles = [...new Set(jsxFiles)];
        const jsxExtensions = ['.jsx', '.tsx', '.js', '.ts'];

        return uniqueFiles.filter((file) => jsxExtensions.some((ext) => file.endsWith(ext)));
    }

    /**
     * Recursively scan a directory for files
     */
    async scanDirectoryRecursive(relativePath: string): Promise<string[]> {
        if (!this.localFileManager) {
            return [];
        }

        const files: string[] = [];

        try {
            const entries = await this.localFileManager.readDir(relativePath);

            for (const entry of entries) {
                const entryPath = relativePath ? `${relativePath}/${entry.name}` : entry.name;

                if (entry.type === 'directory') {
                    // Skip common directories that typically don't contain source files
                    if (!EXCLUDED_SYNC_DIRECTORIES.includes(entry.name)) {
                        const subFiles = await this.scanDirectoryRecursive(entryPath);
                        files.push(...subFiles);
                    }
                } else if (entry.type === 'file') {
                    files.push(entryPath);
                }
            }
        } catch (error) {
            console.debug(`Error scanning directory ${relativePath}:`, error);
        }

        return files;
    }

    clear() {
        // Clear any pending index timeout
        if (this.indexTimeout) {
            clearTimeout(this.indexTimeout);
            this.indexTimeout = null;
        }

        this.fileWatcher?.dispose();
        this.fileWatcher = null;
        this.fileSync.clear();
        this.templateNodeMap.clear();
        this.session.clear();
        this.isIndexed = false;
        this.isIndexing = false;
    }
}
