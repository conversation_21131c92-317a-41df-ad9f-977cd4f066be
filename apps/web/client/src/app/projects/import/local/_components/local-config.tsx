'use client';

import { Button } from '@onlook/ui/button';
import { Input } from '@onlook/ui/input';
import { Label } from '@onlook/ui/label';
import { Icons } from '@onlook/ui/icons';
import { useState } from 'react';
import { useProjectCreation } from '../_context';

export function LocalProjectConfig() {
    const { projectData, setProjectData, nextStep, prevStep } = useProjectCreation();
    const [localPort, setLocalPort] = useState(projectData.localPort || '3000');
    const [error, setError] = useState<string | null>(null);

    const handleNext = () => {
        if (!localPort || isNaN(Number(localPort))) {
            setError('Please enter a valid port number');
            return;
        }
        
        const port = Number(localPort);
        if (port < 1 || port > 65535) {
            setError('Port must be between 1 and 65535');
            return;
        }

        setProjectData({ 
            localPort,
            isLocal: true 
        });
        nextStep();
    };

    return (
        <div className="space-y-6">
            <div>
                <h2 className="text-lg font-semibold mb-2">Local Development Setup</h2>
                <p className="text-sm text-muted-foreground">
                    Configure your local development server. Make sure your app is running on the specified port.
                </p>
            </div>

            <div className="space-y-4">
                <div className="space-y-2">
                    <Label htmlFor="port">Local Port</Label>
                    <Input
                        id="port"
                        type="number"
                        placeholder="3000"
                        value={localPort}
                        onChange={(e) => {
                            setLocalPort(e.target.value);
                            setError(null);
                        }}
                        className={error ? 'border-red-500' : ''}
                    />
                    {error && (
                        <p className="text-sm text-red-500">{error}</p>
                    )}
                    <p className="text-xs text-muted-foreground">
                        The port where your local development server is running
                    </p>
                </div>

                <div className="bg-muted/50 rounded-lg p-4">
                    <div className="flex items-start space-x-2">
                        <Icons.InfoCircled className="w-4 h-4 text-muted-foreground mt-0.5" />
                        <div className="text-sm text-muted-foreground">
                            <p className="font-medium mb-1">Before continuing:</p>
                            <ul className="list-disc list-inside space-y-1">
                                <li>Start your development server (e.g., npm run dev)</li>
                                <li>Make sure it\'s accessible at http://localhost:{localPort || '3000'}</li>
                                <li>Ensure your project has the Onlook script installed</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div className="flex justify-between">
                <Button variant="outline" onClick={prevStep}>
                    Back
                </Button>
                <Button onClick={handleNext}>
                    Continue
                </Button>
            </div>
        </div>
    );
}