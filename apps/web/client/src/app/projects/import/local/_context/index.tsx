'use client';

import {
    ProcessedFileType,
    type NextJsProjectValidation,
    type ProcessedFile,
} from '@/app/projects/types';
import { api } from '@/trpc/react';
import { Routes } from '@/utils/constants';
import { type SandboxBrowserSession, type WebSocketSession } from '@codesandbox/sdk';
import { connectToSandbox } from '@codesandbox/sdk/browser';
import { SandboxTemplates, Templates } from '@onlook/constants';
import { generate, injectPreloadScript, parse, addOidsToAst, getContentFromAst } from '@onlook/parser';
import { useRouter } from 'next/navigation';
import { readFileAction, writeFileAction, fileExistsAction, readDirAction } from '@/app/_server/actions/file';
import * as path from 'path';
import type { ReactNode } from 'react';
import { createContext, useContext, useState } from 'react';

export interface Project {
    name: string;
    folderPath: string;
    files: ProcessedFile[];
    isLocal?: boolean;
    localPort?: string;
    localPath?: string;
}

interface ProjectCreationContextValue {
    // State
    currentStep: number;
    projectData: Partial<Project>;
    direction: number;
    isFinalizing: boolean;
    totalSteps: number;

    // Actions
    error: string | null;
    setProjectData: (newData: Partial<Project>) => void;
    nextStep: () => void;
    prevStep: () => void;
    setCurrentStep: (step: number) => void;
    setDirection: (direction: number) => void;
    resetProjectData: () => void;
    retry: () => void;
    cancel: () => void;
    validateNextJsProject: (files: ProcessedFile[]) => Promise<NextJsProjectValidation>;
}

/**
 * Inject preload script into layout files for local projects
 */
async function injectPreloadScriptForLocalProject(localPath: string) {
    const layoutPaths = [
        'app/layout.tsx',
        'app/layout.jsx', 
        'src/app/layout.tsx',
        'src/app/layout.jsx',
    ];

    for (const layoutPath of layoutPaths) {
        const fullPath = path.join(localPath, layoutPath);
        
        try {
            const fileExists = await fileExistsAction(fullPath);
            if (!fileExists) {
                continue;
            }

            console.log(`Injecting preload script into: ${layoutPath}`);
            
            const content = await readFileAction(fullPath);
            
            try {
                const ast = parse(content, {
                    sourceType: 'module',
                    plugins: ['jsx', 'typescript'],
                });
                const modifiedAst = injectPreloadScript(ast);
                const modifiedContent = generate(modifiedAst, {}, content).code;
                
                // Verify the script was injected
                if (modifiedContent.includes('https://cdn.jsdelivr.net/gh/onlook-dev/onlook@main/apps/web/preload/dist/index.js')) {
                    console.log(`✅ Preload script successfully injected into ${layoutPath}`);
                } else {
                    console.warn(`⚠️  Preload script may not have been injected into ${layoutPath}`);
                }
                
                await writeFileAction(fullPath, modifiedContent);
                console.log(`Successfully injected preload script into: ${layoutPath}`);
            } catch (parseError) {
                console.warn(`Failed to inject preload script into ${layoutPath}:`, parseError);
            }
        } catch (error) {
            console.error(`Error processing layout file ${layoutPath}:`, error);
        }
    }
}

/**
 * Add OIDs to all JSX/TSX files in the local project
 */
async function addOidsToLocalProjectFiles(localPath: string) {
    console.log('🔍 Starting OID addition process for local project...');
    console.log('📁 Local path:', localPath);
    
    try {
        // Scan for JSX/TSX files recursively
        const jsxFiles = await scanForJsxFiles(localPath);
        console.log(`📄 Found ${jsxFiles.length} JSX/TSX files to process:`, jsxFiles);
        
        let totalProcessed = 0;
        let totalModified = 0;
        
        for (const filePath of jsxFiles) {
            try {
                console.log(`🔄 Processing file: ${filePath}`);
                const fullPath = path.join(localPath, filePath);
                
                // Read file content
                const content = await readFileAction(fullPath);
                console.log(`📖 Read file ${filePath}, length: ${content.length} characters`);
                
                // Parse to AST
                const ast = parse(content, {
                    sourceType: 'module',
                    plugins: ['jsx', 'typescript'],
                });
                console.log(`🌳 Parsed AST for ${filePath}`);
                
                // Add OIDs
                const { ast: astWithIds, modified } = addOidsToAst(ast);
                console.log(`🏷️  OID processing for ${filePath}: modified=${modified}`);
                
                if (modified) {
                    const modifiedContent = await getContentFromAst(astWithIds);
                    console.log(`✏️  Generated modified content for ${filePath}, length: ${modifiedContent.length} characters`);
                    
                    await writeFileAction(fullPath, modifiedContent);
                    console.log(`✅ Successfully added OIDs to: ${filePath}`);
                    totalModified++;
                } else {
                    console.log(`⚪ No JSX elements found or OIDs already exist in: ${filePath}`);
                }
                totalProcessed++;
            } catch (error) {
                console.error(`❌ Error processing file ${filePath}:`, error);
            }
        }
        
        console.log(`🎉 Finished adding OIDs to local project files. Processed: ${totalProcessed}, Modified: ${totalModified}`);
        
        if (totalModified > 0) {
            console.log('⚠️  IMPORTANT: Files have been modified with OIDs. If your dev server is running, you may need to restart it to see the changes.');
        }
    } catch (error) {
        console.error('💥 Error scanning for JSX files:', error);
    }
}

/**
 * Recursively scan for JSX/TSX files in a local project
 */
async function scanForJsxFiles(localPath: string): Promise<string[]> {
    const jsxFiles: string[] = [];
    const jsxExtensions = ['.jsx', '.tsx'];
    
    const scanDirectory = async (dirPath: string): Promise<void> => {
        try {
            const entries = await readDirAction(dirPath);
            
            for (const entry of entries) {
                const fullEntryPath = path.join(dirPath, entry.name);
                const relativePath = path.relative(localPath, fullEntryPath);
                
                if (entry.type === 'directory') {
                    // Skip common directories that typically don't contain source files
                    const skipDirs = ['node_modules', '.git', '.next', 'dist', 'build', '.vercel', 'coverage'];
                    if (!skipDirs.includes(entry.name)) {
                        await scanDirectory(fullEntryPath);
                    }
                } else if (entry.type === 'file') {
                    // Check if it's a JSX/TSX file
                    if (jsxExtensions.some(ext => entry.name.endsWith(ext))) {
                        jsxFiles.push(relativePath);
                    }
                }
            }
        } catch (error) {
            console.debug(`Error scanning directory ${dirPath}:`, error);
        }
    };
    
    await scanDirectory(localPath);
    return jsxFiles;
}

const ProjectCreationContext = createContext<ProjectCreationContextValue | undefined>(undefined);

interface ProjectCreationProviderProps {
    children: ReactNode;
    totalSteps: number;
}

export const ProjectCreationProvider = ({ children, totalSteps }: ProjectCreationProviderProps) => {
    const router = useRouter();
    const [currentStep, setCurrentStep] = useState(0);
    const [projectData, setProjectDataState] = useState<Partial<Project>>({
        name: '',
        folderPath: '',
        files: [],
        localPath: '',
    });
    const [error, setError] = useState<string | null>(null);
    const [direction, setDirection] = useState(0);
    const [isFinalizing, setIsFinalizing] = useState(false);
    const { data: user } = api.user.get.useQuery();
    const { mutateAsync: createProject } = api.project.create.useMutation();
    const { mutateAsync: forkSandbox } = api.sandbox.fork.useMutation();
    const { mutateAsync: startSandbox } = api.sandbox.start.useMutation();

    const setProjectData = (newData: Partial<Project>) => {
        setProjectDataState((prevData) => ({ ...prevData, ...newData }));
    };

    const finalizeProject = async () => {
        try {
            setIsFinalizing(true);

            if (!user?.id) {
                console.error('No user found');
                return;
            }
            if (!projectData.files && !projectData.isLocal) {
                return;
            }

            let project;

            if (projectData.isLocal && projectData.localPort) {
                const localPath = projectData.localPath ?? '';

                // Validate that we have a proper absolute path
                if (!localPath) {
                    throw new Error('Local path is required for local projects');
                }

                if (localPath.includes('[username]')) {
                    throw new Error(
                        'Please replace [username] with your actual username in the project path',
                    );
                }

                const projectToCreate = {
                    name: projectData.name ?? 'New project',
                    sandboxId: null,
                    sandboxUrl: null,
                    localPath: localPath,
                    localPort: projectData.localPort,
                    isLocal: 'true',
                    description: 'Your local project',
                };

                console.log('Creating local project with data:', projectToCreate);
                console.log('localPath being saved to database:', projectToCreate.localPath);

                // Create local project
                project = await createProject({
                    project: projectToCreate,
                    userId: user.id,
                });

                // Inject preload script into layout files and add OIDs to all JSX files
                console.log('🚀 Starting local project setup...');
                
                try {
                    console.log('📜 Step 1: Injecting preload script...');
                    await injectPreloadScriptForLocalProject(localPath);
                    console.log('✅ Preload script injection completed');
                    
                    console.log('🏷️  Step 2: Adding OIDs to JSX files...');
                    await addOidsToLocalProjectFiles(localPath);
                    console.log('✅ OID addition completed');
                    
                    console.log('🎉 Local project setup completed successfully!');
                } catch (setupError) {
                    console.error('💥 Error during local project setup:', setupError);
                    throw setupError;
                }
            } else {
                // Original sandbox flow
                const template = SandboxTemplates[Templates.BLANK];
                const forkedSandbox = await forkSandbox({
                    sandbox: {
                        id: template.id,
                        port: template.port,
                    },
                    config: {
                        title: `Imported project - ${user.id}`,
                        tags: ['imported', 'local', user.id],
                    },
                });

                const browserSession: SandboxBrowserSession = await startSandbox({
                    sandboxId: forkedSandbox.sandboxId,
                    userId: user.id,
                });

                const session = await connectToSandbox({
                    session: browserSession,
                    getSession: async (id) => {
                        return await startSandbox({
                            sandboxId: id,
                            userId: user.id,
                        });
                    },
                });

                await uploadToSandbox(projectData.files || [], session);
                await session.setup.run();
                await session.setup.waitUntilComplete();
                await session.disconnect();

                project = await createProject({
                    project: {
                        name: projectData.name ?? 'New project',
                        sandboxId: forkedSandbox.sandboxId,
                        sandboxUrl: forkedSandbox.previewUrl,
                        description: 'Your new project',
                    },
                    userId: user.id,
                });
            }
            if (!project) {
                console.error('Failed to create project');
                return;
            }
            // Open the project
            router.push(`${Routes.PROJECT}/${project.id}`);
        } catch (error) {
            console.error('Error creating project:', error);
            setError('Failed to create project');
            return;
        } finally {
            setIsFinalizing(false);
        }
    };

    const validateNextJsProject = async (
        files: ProcessedFile[],
    ): Promise<NextJsProjectValidation> => {
        const packageJsonFile = files.find(
            (f) => f.path.endsWith('package.json') && f.type === ProcessedFileType.TEXT,
        );

        if (!packageJsonFile) {
            return { isValid: false, error: 'No package.json found' };
        }

        try {
            const packageJson = JSON.parse(packageJsonFile.content as string);
            const hasNext = packageJson.dependencies?.next || packageJson.devDependencies?.next;
            if (!hasNext) {
                return { isValid: false, error: 'Next.js not found in dependencies' };
            }

            const hasReact = packageJson.dependencies?.react || packageJson.devDependencies?.react;
            if (!hasReact) {
                return { isValid: false, error: 'React not found in dependencies' };
            }

            let routerType: 'app' | 'pages' = 'pages';

            const hasAppLayout = files.some(
                (f) =>
                    (f.path.includes('app/layout.') || f.path.includes('src/app/layout.')) &&
                    (f.path.endsWith('.tsx') ||
                        f.path.endsWith('.ts') ||
                        f.path.endsWith('.jsx') ||
                        f.path.endsWith('.js')),
            );

            if (hasAppLayout) {
                routerType = 'app';
            } else {
                // Check for Pages Router (pages directory)
                const hasPagesDir = files.some(
                    (f) => f.path.includes('pages/') || f.path.includes('src/pages/'),
                );

                if (!hasPagesDir) {
                    return {
                        isValid: false,
                        error: 'No valid Next.js router structure found (missing app/ or pages/ directory)',
                    };
                }
            }

            return { isValid: true, routerType };
        } catch (error) {
            return { isValid: false, error: 'Invalid package.json format' };
        }
    };

    const nextStep = () => {
        if (currentStep < totalSteps - 2) {
            // -2 because we have 2 final steps
            setDirection(1);
            setCurrentStep((prev) => prev + 1);
        } else {
            // This is the final step, so we should finalize the project
            setCurrentStep((prev) => prev + 1);
            finalizeProject();
        }
    };

    const prevStep = () => {
        if (currentStep === 0) {
            resetProjectData();
            return;
        }
        setDirection(-1);
        setCurrentStep((prev) => prev - 1);
    };

    const resetProjectData = () => {
        setProjectData({
            folderPath: undefined,
            name: undefined,
            files: undefined,
            localPath: undefined,
        });
        setCurrentStep(0);
        setError(null);
    };

    const retry = () => {
        setError(null);
        finalizeProject();
    };

    const cancel = () => {
        resetProjectData();
    };

    const value: ProjectCreationContextValue = {
        currentStep,
        projectData,
        direction,
        isFinalizing,
        totalSteps,
        error,
        setProjectData,
        nextStep,
        prevStep,
        setCurrentStep,
        setDirection,
        resetProjectData,
        retry,
        cancel,
        validateNextJsProject,
    };

    return (
        <ProjectCreationContext.Provider value={value}>{children}</ProjectCreationContext.Provider>
    );
};

export const useProjectCreation = (): ProjectCreationContextValue => {
    const context = useContext(ProjectCreationContext);
    if (context === undefined) {
        throw new Error('useProjectCreation must be used within a ProjectCreationProvider');
    }
    return context;
};

export const uploadToSandbox = async (files: ProcessedFile[], session: WebSocketSession) => {
    for (const file of files) {
        try {
            if (file.type === ProcessedFileType.BINARY) {
                const uint8Array = new Uint8Array(file.content);
                await session.fs.writeFile(file.path, uint8Array, {
                    overwrite: true,
                });
            } else {
                let content = file.content;

                const isLayout =
                    file.path.endsWith('app/layout.tsx') ||
                    file.path.endsWith('src/app/layout.tsx');
                if (isLayout) {
                    try {
                        const ast = parse(content, {
                            sourceType: 'module',
                            plugins: ['jsx', 'typescript'],
                        });
                        const modifiedAst = injectPreloadScript(ast);
                        content = generate(modifiedAst, {}, content).code;
                    } catch (parseError) {
                        console.warn('Failed to add script config to layout.tsx:', parseError);
                    }
                }
                await session.fs.writeTextFile(file.path, content, {
                    overwrite: true,
                });
            }
        } catch (fileError) {
            console.error(`Error uploading file ${file.path}:`, fileError);
            throw new Error(
                `Failed to upload file: ${file.path} - ${fileError instanceof Error ? fileError.message : 'Unknown error'}`,
            );
        }
    }
};
