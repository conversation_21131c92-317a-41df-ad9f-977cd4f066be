'use server';

import fs from 'fs/promises';
import path from 'path';

export async function writeFileAction(filePath: string, content: string, isBinary = false): Promise<void> {
    try {
        console.debug(`writeFileAction: attempting to write file at path: "${filePath}"`);
        
        // Only resolve relative paths, leave absolute paths as-is
        let resolvedPath = filePath;
        if (!path.isAbsolute(filePath)) {
            resolvedPath = path.resolve(process.cwd(), filePath);
            console.debug(`writeFileAction: resolved relative path "${filePath}" to "${resolvedPath}"`);
        } else {
            console.debug(`writeFileAction: using absolute path "${filePath}" as-is`);
        }
        
        // Ensure the directory exists
        const dir = path.dirname(resolvedPath);
        await fs.mkdir(dir, { recursive: true });

        if (isBinary) {
            // Convert base64 back to buffer for binary files
            const buffer = Buffer.from(content, 'base64');
            await fs.writeFile(resolvedPath, buffer);
        } else {
            await fs.writeFile(resolvedPath, content, 'utf-8');
        }
        
        console.debug(`writeFileAction: successfully wrote file to "${resolvedPath}"`);
    } catch (error) {
        console.error(`writeFileAction: Error writing file "${filePath}":`, error);
        throw new Error(`Failed to write file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

export async function readFileAction(filePath: string): Promise<string> {
    try {
        console.debug(`readFileAction: attempting to read file at path: "${filePath}"`);
        
        // Validate the file path
        if (!filePath) {
            throw new Error('File path is empty or undefined');
        }
        
        // Only resolve relative paths, leave absolute paths as-is
        let resolvedPath = filePath;
        if (!path.isAbsolute(filePath)) {
            resolvedPath = path.resolve(process.cwd(), filePath);
            console.debug(`readFileAction: resolved relative path "${filePath}" to "${resolvedPath}"`);
        } else {
            console.debug(`readFileAction: using absolute path "${filePath}" as-is`);
        }
        
        const content = await fs.readFile(resolvedPath, 'utf-8');
        console.debug(`readFileAction: successfully read file, content length: ${content.length}`);
        return content;
    } catch (error) {
        console.error(`readFileAction: Error reading file "${filePath}":`, error);
        throw new Error(`Failed to read file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

export async function fileExistsAction(filePath: string): Promise<boolean> {
    try {
        console.debug(`fileExistsAction: checking if file exists at path: "${filePath}"`);
        
        if (!filePath) {
            console.debug('fileExistsAction: file path is empty, returning false');
            return false;
        }
        
        // Only resolve relative paths, leave absolute paths as-is
        let resolvedPath = filePath;
        if (!path.isAbsolute(filePath)) {
            resolvedPath = path.resolve(process.cwd(), filePath);
            console.debug(`fileExistsAction: resolved relative path "${filePath}" to "${resolvedPath}"`);
        } else {
            console.debug(`fileExistsAction: using absolute path "${filePath}" as-is`);
        }
        
        await fs.access(resolvedPath);
        console.debug(`fileExistsAction: file exists at path: "${resolvedPath}"`);
        return true;
    } catch (error) {
        console.debug(`fileExistsAction: file does not exist at path: "${filePath}" - ${error instanceof Error ? error.message : 'Unknown error'}`);
        return false;
    }
}

export async function readDirAction(dirPath: string): Promise<{ name: string; type: 'file' | 'directory' }[]> {
    try {
        console.debug(`readDirAction: attempting to read directory at path: "${dirPath}"`);
        
        // Only resolve relative paths, leave absolute paths as-is
        let resolvedPath = dirPath;
        if (!path.isAbsolute(dirPath)) {
            resolvedPath = path.resolve(process.cwd(), dirPath);
            console.debug(`readDirAction: resolved relative path "${dirPath}" to "${resolvedPath}"`);
        } else {
            console.debug(`readDirAction: using absolute path "${dirPath}" as-is`);
        }
        
        const entries = await fs.readdir(resolvedPath, { withFileTypes: true });
        const result = entries.map(entry => ({
            name: entry.name,
            type: entry.isDirectory() ? 'directory' as const : 'file' as const
        }));
        
        console.debug(`readDirAction: successfully read directory, found ${result.length} entries`);
        return result;
    } catch (error) {
        console.error(`readDirAction: Error reading directory "${dirPath}":`, error);
        throw new Error(`Failed to read directory: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}