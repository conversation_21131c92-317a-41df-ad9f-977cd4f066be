{"id": "ee92d761-f1e2-4588-a659-34e2f8c57e5a", "prevId": "7fe865fd-ad69-4633-8197-fce5dda31a09", "version": "7", "dialect": "postgresql", "tables": {"public.canvas": {"name": "canvas", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"canvas_project_id_projects_id_fk": {"name": "canvas_project_id_projects_id_fk", "tableFrom": "canvas", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.frames": {"name": "frames", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "canvas_id": {"name": "canvas_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "frame_type", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "x": {"name": "x", "type": "numeric", "primaryKey": false, "notNull": true}, "y": {"name": "y", "type": "numeric", "primaryKey": false, "notNull": true}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": true}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"frames_canvas_id_canvas_id_fk": {"name": "frames_canvas_id_canvas_id_fk", "tableFrom": "frames", "tableTo": "canvas", "columnsFrom": ["canvas_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.conversations": {"name": "conversations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"conversations_project_id_projects_id_fk": {"name": "conversations_project_id_projects_id_fk", "tableFrom": "conversations", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "conversation_id": {"name": "conversation_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "role": {"name": "role", "type": "role", "primaryKey": false, "notNull": true}, "applied": {"name": "applied", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "snapshots": {"name": "snapshots", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "context": {"name": "context", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "parts": {"name": "parts", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "commit_oid": {"name": "commit_oid", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"messages_conversation_id_conversations_id_fk": {"name": "messages_conversation_id_conversations_id_fk", "tableFrom": "messages", "tableTo": "conversations", "columnsFrom": ["conversation_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.project_create_requests": {"name": "project_create_requests", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "context": {"name": "context", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "project_create_status", "primaryKey": false, "notNull": true, "default": "'pending'"}}, "indexes": {}, "foreignKeys": {"project_create_requests_project_id_projects_id_fk": {"name": "project_create_requests_project_id_projects_id_fk", "tableFrom": "project_create_requests", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"project_create_requests_project_id_unique": {"name": "project_create_requests_project_id_unique", "nullsNotDistinct": false, "columns": ["project_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.deployments": {"name": "deployments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "requested_by": {"name": "requested_by", "type": "uuid", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "sandbox_id": {"name": "sandbox_id", "type": "text", "primaryKey": false, "notNull": false}, "urls": {"name": "urls", "type": "text[]", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "deployment_type", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "deployment_status", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "build_log": {"name": "build_log", "type": "text", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "progress": {"name": "progress", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"deployments_requested_by_users_id_fk": {"name": "deployments_requested_by_users_id_fk", "tableFrom": "deployments", "tableTo": "users", "columnsFrom": ["requested_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "deployments_project_id_projects_id_fk": {"name": "deployments_project_id_projects_id_fk", "tableFrom": "deployments", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.custom_domains": {"name": "custom_domains", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "apex_domain": {"name": "apex_domain", "type": "text", "primaryKey": false, "notNull": true}, "verified": {"name": "verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"custom_domains_apex_domain_unique": {"name": "custom_domains_apex_domain_unique", "nullsNotDistinct": false, "columns": ["apex_domain"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.preview_domains": {"name": "preview_domains", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "full_domain": {"name": "full_domain", "type": "text", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"preview_domains_project_id_projects_id_fk": {"name": "preview_domains_project_id_projects_id_fk", "tableFrom": "preview_domains", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"preview_domains_full_domain_unique": {"name": "preview_domains_full_domain_unique", "nullsNotDistinct": false, "columns": ["full_domain"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.published_domains": {"name": "published_domains", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "domain_id": {"name": "domain_id", "type": "uuid", "primaryKey": false, "notNull": false}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "full_domain": {"name": "full_domain", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"published_domains_domain_id_custom_domains_id_fk": {"name": "published_domains_domain_id_custom_domains_id_fk", "tableFrom": "published_domains", "tableTo": "custom_domains", "columnsFrom": ["domain_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "published_domains_project_id_projects_id_fk": {"name": "published_domains_project_id_projects_id_fk", "tableFrom": "published_domains", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"published_domains_domain_id_unique": {"name": "published_domains_domain_id_unique", "nullsNotDistinct": false, "columns": ["domain_id"]}, "published_domains_full_domain_unique": {"name": "published_domains_full_domain_unique", "nullsNotDistinct": false, "columns": ["full_domain"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.custom_domain_verification": {"name": "custom_domain_verification", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "domain_id": {"name": "domain_id", "type": "uuid", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "verification_id": {"name": "verification_id", "type": "text", "primaryKey": false, "notNull": true}, "verification_code": {"name": "verification_code", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "verification_request_status", "primaryKey": false, "notNull": true, "default": "'active'"}}, "indexes": {}, "foreignKeys": {"custom_domain_verification_domain_id_custom_domains_id_fk": {"name": "custom_domain_verification_domain_id_custom_domains_id_fk", "tableFrom": "custom_domain_verification", "tableTo": "custom_domains", "columnsFrom": ["domain_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "custom_domain_verification_project_id_projects_id_fk": {"name": "custom_domain_verification_project_id_projects_id_fk", "tableFrom": "custom_domain_verification", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.project_invitations": {"name": "project_invitations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "inviter_id": {"name": "inviter_id", "type": "uuid", "primaryKey": false, "notNull": true}, "invitee_email": {"name": "invitee_email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "project_role", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"project_invitations_project_id_projects_id_fk": {"name": "project_invitations_project_id_projects_id_fk", "tableFrom": "project_invitations", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "project_invitations_inviter_id_users_id_fk": {"name": "project_invitations_inviter_id_users_id_fk", "tableFrom": "project_invitations", "tableTo": "users", "columnsFrom": ["inviter_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"project_invitations_token_unique": {"name": "project_invitations_token_unique", "nullsNotDistinct": false, "columns": ["token"]}, "project_invitations_invitee_email_project_id_unique": {"name": "project_invitations_invitee_email_project_id_unique", "nullsNotDistinct": false, "columns": ["invitee_email", "project_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "preview_img_url": {"name": "preview_img_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "preview_img_path": {"name": "preview_img_path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "preview_img_bucket": {"name": "preview_img_bucket", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sandbox_id": {"name": "sandbox_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "sandbox_url": {"name": "sandbox_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.project_settings": {"name": "project_settings", "schema": "", "columns": {"project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "run_command": {"name": "run_command", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "build_command": {"name": "build_command", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "install_command": {"name": "install_command", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}}, "indexes": {}, "foreignKeys": {"project_settings_project_id_projects_id_fk": {"name": "project_settings_project_id_projects_id_fk", "tableFrom": "project_settings", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"project_settings_project_id_unique": {"name": "project_settings_project_id_unique", "nullsNotDistinct": false, "columns": ["project_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.prices": {"name": "prices", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "price_key": {"name": "price_key", "type": "price_keys", "primaryKey": false, "notNull": true}, "monthly_message_limit": {"name": "monthly_message_limit", "type": "integer", "primaryKey": false, "notNull": true}, "stripe_price_id": {"name": "stripe_price_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"prices_product_id_products_id_fk": {"name": "prices_product_id_products_id_fk", "tableFrom": "prices", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"prices_stripe_price_id_unique": {"name": "prices_stripe_price_id_unique", "nullsNotDistinct": false, "columns": ["stripe_price_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.products": {"name": "products", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "product_type", "primaryKey": false, "notNull": true}, "stripe_product_id": {"name": "stripe_product_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"products_stripe_product_id_unique": {"name": "products_stripe_product_id_unique", "nullsNotDistinct": false, "columns": ["stripe_product_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.subscriptions": {"name": "subscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "product_id": {"name": "product_id", "type": "uuid", "primaryKey": false, "notNull": true}, "price_id": {"name": "price_id", "type": "uuid", "primaryKey": false, "notNull": true}, "started_at": {"name": "started_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "ended_at": {"name": "ended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": true}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": true}, "stripe_subscription_item_id": {"name": "stripe_subscription_item_id", "type": "text", "primaryKey": false, "notNull": true}, "scheduled_action": {"name": "scheduled_action", "type": "scheduled_subscription_action", "primaryKey": false, "notNull": false}, "scheduled_price_id": {"name": "scheduled_price_id", "type": "uuid", "primaryKey": false, "notNull": false}, "scheduled_change_at": {"name": "scheduled_change_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "stripe_subscription_schedule_id": {"name": "stripe_subscription_schedule_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"subscriptions_user_id_users_id_fk": {"name": "subscriptions_user_id_users_id_fk", "tableFrom": "subscriptions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "subscriptions_product_id_products_id_fk": {"name": "subscriptions_product_id_products_id_fk", "tableFrom": "subscriptions", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "subscriptions_price_id_prices_id_fk": {"name": "subscriptions_price_id_prices_id_fk", "tableFrom": "subscriptions", "tableTo": "prices", "columnsFrom": ["price_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "subscriptions_scheduled_price_id_prices_id_fk": {"name": "subscriptions_scheduled_price_id_prices_id_fk", "tableFrom": "subscriptions", "tableTo": "prices", "columnsFrom": ["scheduled_price_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"subscriptions_stripe_subscription_id_unique": {"name": "subscriptions_stripe_subscription_id_unique", "nullsNotDistinct": false, "columns": ["stripe_subscription_id"]}, "subscriptions_stripe_subscription_item_id_unique": {"name": "subscriptions_stripe_subscription_item_id_unique", "nullsNotDistinct": false, "columns": ["stripe_subscription_item_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.usage_records": {"name": "usage_records", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "usage_types", "primaryKey": false, "notNull": true, "default": "'message'"}, "timestamp": {"name": "timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"usage_records_user_id_users_id_fk": {"name": "usage_records_user_id_users_id_fk", "tableFrom": "usage_records", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "auth.users": {"name": "users", "schema": "auth", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_confirmed_at": {"name": "email_confirmed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "raw_user_meta_data": {"name": "raw_user_meta_data", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.user_settings": {"name": "user_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "auto_apply_code": {"name": "auto_apply_code", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "expand_code_blocks": {"name": "expand_code_blocks", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_suggestions": {"name": "show_suggestions", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_mini_chat": {"name": "show_mini_chat", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "should_warn_delete": {"name": "should_warn_delete", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {"user_settings_user_id_users_id_fk": {"name": "user_settings_user_id_users_id_fk", "tableFrom": "user_settings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_settings_user_id_unique": {"name": "user_settings_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"users_id_users_id_fk": {"name": "users_id_users_id_fk", "tableFrom": "users", "tableTo": "users", "schemaTo": "auth", "columnsFrom": ["id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.user_canvases": {"name": "user_canvases", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "canvas_id": {"name": "canvas_id", "type": "uuid", "primaryKey": false, "notNull": true}, "scale": {"name": "scale", "type": "numeric", "primaryKey": false, "notNull": true}, "x": {"name": "x", "type": "numeric", "primaryKey": false, "notNull": true}, "y": {"name": "y", "type": "numeric", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_canvases_user_id_users_id_fk": {"name": "user_canvases_user_id_users_id_fk", "tableFrom": "user_canvases", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "user_canvases_canvas_id_canvas_id_fk": {"name": "user_canvases_canvas_id_canvas_id_fk", "tableFrom": "user_canvases", "tableTo": "canvas", "columnsFrom": ["canvas_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"user_canvases_user_id_canvas_id_pk": {"name": "user_canvases_user_id_canvas_id_pk", "columns": ["user_id", "canvas_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}, "public.user_projects": {"name": "user_projects", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "role": {"name": "role", "type": "project_role", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_projects_user_id_users_id_fk": {"name": "user_projects_user_id_users_id_fk", "tableFrom": "user_projects", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "user_projects_project_id_projects_id_fk": {"name": "user_projects_project_id_projects_id_fk", "tableFrom": "user_projects", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"user_projects_user_id_project_id_pk": {"name": "user_projects_user_id_project_id_pk", "columns": ["user_id", "project_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": true}}, "enums": {"public.frame_type": {"name": "frame_type", "schema": "public", "values": ["web"]}, "public.role": {"name": "role", "schema": "public", "values": ["user", "assistant", "system"]}, "public.project_create_status": {"name": "project_create_status", "schema": "public", "values": ["pending", "completed", "failed"]}, "public.deployment_status": {"name": "deployment_status", "schema": "public", "values": ["in_progress", "completed", "failed"]}, "public.deployment_type": {"name": "deployment_type", "schema": "public", "values": ["preview", "custom", "unpublish_preview", "unpublish_custom"]}, "public.verification_request_status": {"name": "verification_request_status", "schema": "public", "values": ["active", "expired", "used"]}, "public.price_keys": {"name": "price_keys", "schema": "public", "values": ["PRO_MONTHLY_TIER_1", "PRO_MONTHLY_TIER_2", "PRO_MONTHLY_TIER_3", "PRO_MONTHLY_TIER_4", "PRO_MONTHLY_TIER_5", "PRO_MONTHLY_TIER_6", "PRO_MONTHLY_TIER_7", "PRO_MONTHLY_TIER_8", "PRO_MONTHLY_TIER_9", "PRO_MONTHLY_TIER_10", "PRO_MONTHLY_TIER_11"]}, "public.product_type": {"name": "product_type", "schema": "public", "values": ["free", "pro"]}, "public.scheduled_subscription_action": {"name": "scheduled_subscription_action", "schema": "public", "values": ["price_change", "cancellation"]}, "public.usage_types": {"name": "usage_types", "schema": "public", "values": ["message", "deployment"]}, "public.project_role": {"name": "project_role", "schema": "public", "values": ["owner", "admin"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}